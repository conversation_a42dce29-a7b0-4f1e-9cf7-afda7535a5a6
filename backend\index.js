const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const pool = require('./db');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const axios = require('axios');
const upload = multer({ dest: 'uploads/' });
const fs = require('fs');
const FormData = require('form-data');
const { createServer } = require('http');
const { Server } = require('socket.io');
const NotificationService = require('./notifications');

// Use axios for HTTP requests (already installed)
// const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Load environment variables
dotenv.config();
console.log('HeyGen API Key:', process.env.HEYGEN_API_KEY); // Added for debugging

// Azure Search and OpenAI Configuration
const AZURE_SEARCH_ENDPOINT = process.env.AZURE_SEARCH_ENDPOINT || "https://trainingvideodemo.search.windows.net";
const AZURE_SEARCH_KEY = process.env.AZURE_SEARCH_KEY || "****************************************************";
const INDEX_NAME = process.env.AZURE_SEARCH_INDEX || "pdf-chunks-index-saitest";

const OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT || "gpt-4o-mini";
const OPENAI_KEY = process.env.AZURE_OPENAI_KEY || "CCBv7rsfAFU3viqlmrWN7QxICh1CbCeSXMbjqD0jMwwwg9J36FqtJQQJ99BFACYeBjFXJ3w3AAAAACOGwpnn";
const OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT || "https://trainingsimula9559570779.openai.azure.com";

const app = express();
const server = createServer(app);

// Initialize Socket.IO with CORS configuration
const io = new Server(server, {
  cors: {
   //origin: "http://localhost:3000", // Frontend URL
    origin: "https://agenticairi-app6.exlservice.com", // Frontend URL
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Initialize notification service
const notificationService = new NotificationService(io);

// Middleware
app.use(cors());
app.use(express.json());

// // Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Handle user joining (optional: for user-specific notifications)
  socket.on('join_user', (userId) => {
    socket.join(`user_${userId}`);
    console.log(`User ${userId} joined their room`);
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Start notification polling when server starts
notificationService.startPolling();

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the sai API' });
});

// Registration endpoint
app.post('/api/register', async (req, res) => {
  const { first_name, last_name, username, email, password } = req.body;
  if (!first_name || !last_name || !username || !email || !password) {
    return res.status(400).json({ error: 'All fields are required.' });
  }
  try {
    // Check if user/email already exists
    const userExists = await pool.query(
      'SELECT * FROM userdetails WHERE username = $1 OR email = $2',
      [username, email]
    );
    if (userExists.rows.length > 0) {
      return res.status(409).json({ error: 'Username or email already exists.' });
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Insert user with role 'user'
    await pool.query(
      'INSERT INTO userdetails (first_name, last_name, username, email, role, password) VALUES ($1, $2, $3, $4, $5, $6)',
      [first_name, last_name, username, email, 'user', hashedPassword]
    );
    res.status(201).json({ message: 'User registered successfully.' });
  } catch (err) {
    res.status(500).json({ error: 'Registration failed.' });
  }
});

// Login endpoint
app.post('/api/login', async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required.' });
  }
  try {
    const userRes = await pool.query('SELECT * FROM userdetails WHERE email = $1', [email]);
    if (userRes.rows.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials.' });
    }
    const user = userRes.rows[0];
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) {
      return res.status(401).json({ error: 'Invalid credentials.' });
    }
    // Generate JWT with all user data
    const token = jwt.sign(
      {
        id: user.id,
        role: user.role,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name
      },
      process.env.JWT_SECRET || 'secret',
      { expiresIn: '1d' }
    );
    res.json({ token, user: { id: user.id, role: user.role, username: user.username, email: user.email, first_name: user.first_name, last_name: user.last_name } });
  } catch (err) {
    res.status(500).json({ error: 'Login failed.' });
  }
});

// Middleware to verify JWT token
const verifyToken = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1]; // Bearer token
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret');
    req.user = decoded;
    next();
  } catch (err) {
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Notification endpoints
app.get('/api/notifications', verifyToken, async (req, res) => {
  try {
    const notifications = await notificationService.getRecentNotifications(req.user.id);
    res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ error: 'Failed to fetch notifications' });
  }
});

// Trigger notification check (for testing)
app.post('/api/notifications/check', verifyToken, async (req, res) => {
  try {
    await notificationService.triggerCheck();
    res.json({ message: 'Notification check triggered' });
  } catch (error) {
    console.error('Error triggering notification check:', error);
    res.status(500).json({ error: 'Failed to trigger notification check' });
  }
});

// Get admin video completion notifications
app.get('/api/admin/notifications', verifyToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Access denied. Admin role required.' });
    }

    const query = `
      SELECT
        vgh.id,
        vgh.course_id,
        vgh.page_title,
        vgh.created_at,
        vgh.video_id,
        vgh.videostatusapi,
        c.title as course_title,
        c.domain
      FROM video_generation_history vgh
      JOIN courses c ON vgh.course_id = c.id
      WHERE vgh.videostatusapi = 'completed'
        AND c.created_by = $1
        AND vgh.created_at >= NOW() - INTERVAL '7 days'
      ORDER BY vgh.created_at DESC
      LIMIT 20
    `;

    const result = await pool.query(query, [req.user.email]);

    // Group by course and format as notifications
    const courseNotifications = new Map();

    result.rows.forEach(row => {
      if (!courseNotifications.has(row.course_id)) {
        courseNotifications.set(row.course_id, {
          id: `admin_video_${row.course_id}_${Date.now()}`,
          type: 'admin_video_completed',
          title: 'Video Generation Completed!',
          message: `"${row.course_title}" video has been completed`,
          courseId: row.course_id,
          courseTitle: row.course_title,
          domain: row.domain,
          timestamp: row.created_at,
          videoCount: 1,
          read: false
        });
      } else {
        const notification = courseNotifications.get(row.course_id);
        notification.videoCount++;
        notification.message = `"${row.course_title}" videos have been completed`;
      }
    });

    const notifications = Array.from(courseNotifications.values());
    res.json(notifications);
  } catch (err) {
    console.error('Error fetching admin notifications:', err);
    res.status(500).json({ error: 'Failed to fetch admin notifications' });
  }
});

// Test endpoint to simulate publishing a video (for testing)
app.post('/api/test/publish-video', verifyToken, async (req, res) => {
  try {
    const { courseId, pageTitle } = req.body;

    if (!courseId) {
      return res.status(400).json({ error: 'Course ID is required' });
    }

    // Update a video_generation_history record to published status
    const updateQuery = `
      UPDATE video_generation_history
      SET video_publish_status = 'published'
      WHERE id = (
        SELECT id FROM video_generation_history
        WHERE course_id = $1
          AND video_publish_status = 'draft'
          AND generation_status = 'success'
        LIMIT 1
      )
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [courseId]);

    if (result.rows.length > 0) {
      // Trigger notification check immediately
      await notificationService.triggerCheck();
      res.json({
        message: 'Video published successfully',
        publishedVideo: result.rows[0]
      });
    } else {
      res.status(404).json({ error: 'No draft videos found for this course' });
    }
  } catch (error) {
    console.error('Error publishing video:', error);
    res.status(500).json({ error: 'Failed to publish video' });
  }
});

// Get current user profile
app.get('/api/user/profile', verifyToken, async (req, res) => {
  try {
    const userRes = await pool.query(
      'SELECT id, first_name, last_name, username, email, role, created_at FROM userdetails WHERE id = $1',
      [req.user.id]
    );
    if (userRes.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(userRes.rows[0]);
  } catch (err) {
    console.error('Error fetching user profile:', err);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});


//course table get me the id, title, description
app.get('/api/coursestitleanddescriptio', async (req, res) => {
  try {
    const result = await pool.query('SELECT id, title, description FROM courses ORDER BY id DESC');
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch courses.' });
  }
});

// Get courses grouped by domain for home page (only published courses)
app.get('/api/courses/by-domain', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT DISTINCT
        c.id,
        c.title,
        c.description,
        c.domain,
        c.level,
        c.estimated_hours,
        c.created_at
      FROM courses c
      INNER JOIN video_generation_history v ON c.id = v.course_id
      WHERE v.video_publish_status = 'published'
      ORDER BY c.domain, c.title
    `);

    // Group courses by domain
    const coursesByDomain = result.rows.reduce((acc, course) => {
      const domain = course.domain || 'Other';
      if (!acc[domain]) {
        acc[domain] = [];
      }
      acc[domain].push(course);
      return acc;
    }, {});

    res.json(coursesByDomain);
  } catch (err) {
    console.error('Error fetching published courses by domain:', err);
    res.status(500).json({ error: 'Failed to fetch published courses by domain.' });
  }
});


// Course creation endpoint
app.post('/api/courses', upload.single('file'), async (req, res) => {
  try {
    const { title, description, domain, level, estimated_hours, created_by } = req.body;
    const file = req.file;
    if (!file) {
      console.error('No PDF file received from frontend.');
      return res.status(400).json({ error: 'PDF file is required.' });
    }
    console.log('Received file:', file.originalname);
    // Forward PDF to external service
    const formData = new FormData();
    formData.append('file', fs.createReadStream(file.path), file.originalname);
    let pdfRes;
    try {
      pdfRes = await axios.post('http://*************:5002/process_pdf', formData, {
        headers: formData.getHeaders(),
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
    } catch (err) {
      console.error('Error calling external PDF endpoint:', err?.response?.data || err.message);
      fs.unlinkSync(file.path);
      return res.status(500).json({ error: 'Failed to process PDF with external service.', details: err?.response?.data || err.message });
    }
    const { blob_url, output, uploaded_filename } = pdfRes.data;
    console.log('External endpoint response:', pdfRes.data);
    // Store in DB
    try {
      await pool.query(
        `INSERT INTO courses (title, description, domain, level, estimated_hours, blob_url, uploaded_filename, pdf_output, created_by)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [title, description, domain, level, estimated_hours, blob_url, uploaded_filename, JSON.stringify(output), created_by]
      );
    } catch (err) {
      console.error('Database insert error:', err.stack || err.message);
      fs.unlinkSync(file.path);
      return res.status(500).json({ error: 'Failed to save course in database.', details: err.stack || err.message });
    }
    // Clean up uploaded file
    fs.unlinkSync(file.path);
    res.status(201).json({ message: 'Course created successfully.', blob_url, uploaded_filename });
  } catch (err) {
    console.error('General error in /api/courses:', err.stack || err.message);
    res.status(500).json({ error: 'Course creation failed.', details: err.stack || err.message });
  }
});

// Get all courses endpoint
app.get('/api/courses', async (req, res) => {
  try {
    const result = await pool.query('SELECT id, title, domain FROM courses ORDER BY id DESC');
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch courses.' });
  }
});

// Get single course by id
app.get('/api/courses/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT * FROM courses WHERE id = $1', [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Course not found.' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch course.' });
  }
});

// Approve all (create a new version)
app.post('/api/courses/:id/approve', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, approved_by, comment } = req.body;
    // Get next version number
    const versionRes = await pool.query(
      'SELECT COALESCE(MAX(version_number), 0) + 1 AS next_version FROM course_content_versions WHERE course_id = $1',
      [id]
    );
    const version_number = versionRes.rows[0].next_version;
    // Insert new version
    await pool.query(
      'INSERT INTO course_content_versions (course_id, version_number, content, approved_by, comment) VALUES ($1, $2, $3, $4, $5)',
      [id, version_number, JSON.stringify(content), approved_by, comment || null]
    );
    res.status(201).json({ message: 'Content approved and versioned.', version_number });
  } catch (err) {
    res.status(500).json({ error: 'Failed to approve content.', details: err.message });
  }
});

// List all versions for a course
app.get('/api/courses/:id/versions', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(
      'SELECT id, version_number, approved_by, approved_at, comment FROM course_content_versions WHERE course_id = $1 ORDER BY version_number DESC',
      [id]
    );
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch versions.' });
  }
});

// Get a specific version for a course
app.get('/api/courses/:id/versions/:version_number', async (req, res) => {
  try {
    const { id, version_number } = req.params;
    const result = await pool.query(
      'SELECT * FROM course_content_versions WHERE course_id = $1 AND version_number = $2',
      [id, version_number]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Version not found.' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch version.' });
  }
});

// Get all approved content versions with course titles
app.get('/api/all-approved-content-versions', async (req, res) => {
  try {
    const result = await pool.query(
      `SELECT
          ccv.id AS version_id,
          ccv.version_number,
          c.id AS course_id,
          c.title AS course_title,
          ccv.approved_at
       FROM
          course_content_versions ccv
       JOIN
          courses c ON ccv.course_id = c.id
       ORDER BY
          c.title, ccv.version_number DESC`
    );
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch approved content versions.', details: err.message });
  }
});

// Get course content version by version ID
app.get('/api/course-content-versions/:versionId', async (req, res) => {
  try {
    const { versionId } = req.params;
    const result = await pool.query(
      'SELECT * FROM course_content_versions WHERE id = $1',
      [versionId]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Content version not found.' });
    }
    res.json(result.rows[0]);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch content version.', details: err.message });
  }
});

// Get all avatar configurations
app.get('/api/avatars', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT id, avatar_name, template_id, character_id, voice_id, specialty, domain FROM avatars_config ORDER BY avatar_name'
    );
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch avatar configurations.', details: err.message });
  }
});

// Create video_generation_history table if it doesn't exist
pool.query(`
  CREATE TABLE IF NOT EXISTS video_generation_history (
    id SERIAL PRIMARY KEY,
    course_id INTEGER NOT NULL,
    version_id INTEGER NOT NULL,
    page_number INTEGER NOT NULL,
    page_title TEXT,
    video_id TEXT,
    generation_status TEXT NOT NULL,
    error_details TEXT,
    avatar_id INTEGER NOT NULL,
    videostatusapi TEXT DEFAULT 'processing',
    heygenvideourl TEXT,
    heygenbloburl TEXT,
    video_publish_status TEXT DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (version_id) REFERENCES course_content_versions(id),
    FOREIGN KEY (avatar_id) REFERENCES avatars_config(id)
  );
`).catch(err => console.error('Error creating video_generation_history table:', err));

// Add video_publish_status column if it doesn't exist (for existing tables)
pool.query(`
  ALTER TABLE video_generation_history
  ADD COLUMN IF NOT EXISTS video_publish_status TEXT DEFAULT 'draft';
`).catch(err => console.error('Error adding video_publish_status column:', err));

// Note: user_course_progress table removed - using only user_module_progress for tracking

// Create user_module_progress table for detailed module tracking
pool.query(`
  CREATE TABLE IF NOT EXISTS user_module_progress (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    module_number INTEGER NOT NULL,
    module_title TEXT,
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    watch_time_seconds INTEGER NOT NULL DEFAULT 0,
    total_duration_seconds INTEGER,
    completion_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    first_watched_at TIMESTAMP,
    last_watched_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES userdetails(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id, module_number)
  );
`).catch(err => console.error('Error creating user_module_progress table:', err));

// Create course_discussions table for discussion forum
pool.query(`
  CREATE TABLE IF NOT EXISTS course_discussions (
    id SERIAL PRIMARY KEY,
    course_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES userdetails(id) ON DELETE CASCADE
  );
`).catch(err => console.error('Error creating course_discussions table:', err));

// Create discussion_replies table for replies to discussions
pool.query(`
  CREATE TABLE IF NOT EXISTS discussion_replies (
    id SERIAL PRIMARY KEY,
    discussion_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (discussion_id) REFERENCES course_discussions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES userdetails(id) ON DELETE CASCADE
  );
`).catch(err => console.error('Error creating discussion_replies table:', err));

// Create pdf_indexer_tracking table for dynamic index names
pool.query(`
  CREATE TABLE IF NOT EXISTS pdf_indexer_tracking (
    id SERIAL PRIMARY KEY,
    course_id INTEGER NOT NULL,
    index_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(course_id)
  );
`).catch(err => console.error('Error creating pdf_indexer_tracking table:', err));

// Video Generation endpoint
app.post('/api/generate-course-videos', async (req, res) => {
  const { selectedAvatarId, selectedVersionId } = req.body;

  if (!selectedAvatarId || !selectedVersionId) {
    return res.status(400).json({ error: 'Avatar and content version must be selected.' });
  }

  // Fetch selected avatar config from DB instead of hardcoded array
  let selectedConfig;
  try {
    const avatarRes = await pool.query(
      'SELECT template_id, character_id, voice_id FROM avatars_config WHERE id = $1',
      [selectedAvatarId]
    );
    if (avatarRes.rows.length === 0) {
      return res.status(404).json({ error: 'Selected avatar configuration not found.' });
    }
    selectedConfig = avatarRes.rows[0];
    console.log('Selected Avatar Config:', selectedConfig); // Debug log
  } catch (dbErr) {
    console.error("Error fetching avatar config:", dbErr);
    return res.status(500).json({ error: 'Failed to retrieve avatar configuration.', details: dbErr.message });
  }

  // Get course_id from the version
  let courseId;
  try {
    const versionRes = await pool.query(
      'SELECT course_id FROM course_content_versions WHERE id = $1',
      [selectedVersionId]
    );
    if (versionRes.rows.length === 0) {
      return res.status(404).json({ error: 'Content version not found.' });
    }
    courseId = versionRes.rows[0].course_id;
    console.log('Course ID:', courseId); // Debug log
  } catch (dbErr) {
    console.error("Error fetching course_id:", dbErr);
    return res.status(500).json({ error: 'Failed to retrieve course information.', details: dbErr.message });
  }

  if (!selectedConfig || !selectedConfig.template_id || !selectedConfig.character_id) {
    console.error('Invalid avatar configuration:', selectedConfig); // Debug log
    return res.status(400).json({ error: 'Invalid avatar configuration fetched from DB.' });
  }

  const heygenApiKey = process.env.HEYGEN_API_KEY;
  if (!heygenApiKey) {
    console.error('HeyGen API Key is missing'); // Debug log
    return res.status(500).json({ error: 'HeyGen API Key not configured.' });
  }

  try {
    // 1. Fetch the content (pages) from the selected version
    const versionRes = await pool.query(
      'SELECT content FROM course_content_versions WHERE id = $1',
      [selectedVersionId]
    );

    if (versionRes.rows.length === 0) {
      return res.status(404).json({ error: 'Content version not found.' });
    }

    let pages = versionRes.rows[0].content;
    if (!Array.isArray(pages)) {
      console.error("Content is not an array:", pages);
      return res.status(500).json({ error: "Approved content format invalid." });
    }

    console.log(`Processing ${pages.length} pages for video generation`); // Debug log

    const heygenHeaders = {
      "Accept": "application/json",
      "X-API-KEY": heygenApiKey,
      "Content-Type": "application/json"
    };

    const generatedVideos = [];

    for (const page of pages) {
      const scriptText = page.aiSummary;
      const imageAssetId = page.imageassestid || "";
      const pageTitle = page.title || `Page ${page.page}`;

      console.log(`Processing page: ${pageTitle}`); // Debug log

      if (!scriptText) {
        console.warn(`Skipping video generation for ${pageTitle} due to empty script.`);
        generatedVideos.push({ ...page, videoid: null, generation_status: "skipped_empty_script" });
        
        await pool.query(
          `INSERT INTO video_generation_history 
          (course_id, version_id, page_number, page_title, video_id, generation_status, error_details, avatar_id, videostatusapi, heygenvideourl, heygenbloburl) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [courseId, selectedVersionId, page.page, pageTitle, null, "skipped_empty_script", null, selectedAvatarId, "processing", null, null]
        );
        continue;
      }

      const heygenPayload = {
        caption: false,
        title: `${pageTitle} - ${selectedConfig.character_id}`,
        variables: {
          character: {
            name: "character",
            type: "character",
            properties: {
              character_id: selectedConfig.character_id,
              voice_id: selectedConfig.voice_id || "",
              type: "avatar",
            }
          },
          cover_image: {
            name: "cover_image",
            type: "image",
            properties: {
              asset_id: imageAssetId,
              fit: "none"
            }
          },
          script: {
            name: "script",
            type: "text",
            properties: {
              content: scriptText,
            }
          }
        }
      };

      const generateUrl = `https://api.heygen.com/v2/template/${selectedConfig.template_id}/generate`;
      console.log(`HeyGen API URL: ${generateUrl}`); // Debug log

      try {
        console.log(`Sending request for ${pageTitle} to HeyGen API...`);
        console.log("HeyGen Payload:", JSON.stringify(heygenPayload, null, 2));

        const heygenResponse = await axios.post(generateUrl, heygenPayload, { 
          headers: heygenHeaders,
          timeout: 30000 // 30 second timeout
        });
        
        const respJson = heygenResponse.data;
        console.log(`HeyGen Response for ${pageTitle}:`, JSON.stringify(respJson, null, 2));

        const videoId = respJson?.data?.video_id;

        if (videoId) {
          generatedVideos.push({ ...page, videoid: videoId, generation_status: "success" });
          
          await pool.query(
            `INSERT INTO video_generation_history 
            (course_id, version_id, page_number, page_title, video_id, generation_status, error_details, avatar_id, videostatusapi, heygenvideourl, heygenbloburl) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
            [courseId, selectedVersionId, page.page, pageTitle, videoId, "success", null, selectedAvatarId, "processing", `https://heygen.com/videos/${videoId}`, null]
          );
          console.log(`Successfully generated video for ${pageTitle}: ${videoId}`);
        } else {
          const errorMsg = respJson?.message || "Unknown HeyGen error";
          console.error(`HeyGen API error for ${pageTitle}:`, errorMsg);
          generatedVideos.push({ ...page, videoid: null, generation_status: "failed", error_details: errorMsg });
          
          await pool.query(
            `INSERT INTO video_generation_history 
            (course_id, version_id, page_number, page_title, video_id, generation_status, error_details, avatar_id, videostatusapi, heygenvideourl, heygenbloburl) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
            [courseId, selectedVersionId, page.page, pageTitle, null, "failed", errorMsg, selectedAvatarId, "failed", null, null]
          );
        }
      } catch (heygenErr) {
        console.error(`HeyGen API error for ${pageTitle}:`, {
          message: heygenErr.message,
          response: heygenErr.response?.data,
          status: heygenErr.response?.status,
          headers: heygenErr.response?.headers
        });

        const errorMsg = heygenErr?.response?.data?.message || heygenErr.message;
        generatedVideos.push({ ...page, videoid: null, generation_status: "failed", error_details: errorMsg });
        
        await pool.query(
          `INSERT INTO video_generation_history 
          (course_id, version_id, page_number, page_title, video_id, generation_status, error_details, avatar_id, videostatusapi, heygenvideourl, heygenbloburl) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
          [courseId, selectedVersionId, page.page, pageTitle, null, "failed", errorMsg, selectedAvatarId, "failed", null, null]
        );
      }
    }

    res.json({ 
      message: 'Video generation process completed.', 
      generated_videos: generatedVideos,
      summary: {
        total: pages.length,
        success: generatedVideos.filter(v => v.generation_status === "success").length,
        failed: generatedVideos.filter(v => v.generation_status === "failed").length,
        skipped: generatedVideos.filter(v => v.generation_status === "skipped_empty_script").length
      }
    });

    // Update the content in course_content_versions with the generated video IDs
    try {
      await pool.query(
        'UPDATE course_content_versions SET content = $1 WHERE id = $2',
        [JSON.stringify(generatedVideos), selectedVersionId]
      );
      console.log(`Updated content for version ${selectedVersionId} with video IDs.`);
    } catch (dbErr) {
      console.error(`Failed to update content for version ${selectedVersionId} with video IDs:`, dbErr.message);
    }

  } catch (err) {
    console.error('Error in /api/generate-course-videos:', {
      message: err.message,
      stack: err.stack,
      response: err.response?.data
    });
    res.status(500).json({ 
      error: 'Video generation failed.', 
      details: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
});

// Get course videos by course ID
app.get('/api/courses/:id/videos', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        vgh.id,
        vgh.page_number,
        vgh.page_title,
        vgh.video_id,
        vgh.generation_status,
        vgh.videostatusapi,
        vgh.heygenvideourl,
        vgh.heygenbloburl,
        vgh.created_at,
        ac.avatar_name,
        ccv.version_number
      FROM video_generation_history vgh
      JOIN course_content_versions ccv ON vgh.version_id = ccv.id
      JOIN avatars_config ac ON vgh.avatar_id = ac.id
      WHERE vgh.course_id = $1
        AND vgh.generation_status = 'success'
        AND vgh.heygenbloburl IS NOT NULL
      ORDER BY vgh.page_number ASC
    `;

    const result = await pool.query(query, [id]);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching course videos:', err);
    res.status(500).json({ error: 'Failed to fetch course videos', details: err.message });
  }
});

// Get video generation history
app.get('/api/video-generation-history', async (req, res) => {
  try {
    const { course_id, page_number, status } = req.query;

    let query = `
      SELECT
        vgh.*,
        c.title as course_title,
        c.description as course_description,
        ccv.version_number,
        ac.avatar_name as avatar_name,
        ccv.version_number as version_number,
        vgh.videostatusapi,
        vgh.heygenvideourl,
        vgh.heygenbloburl,
        vgh.video_publish_status
      FROM video_generation_history vgh
      JOIN courses c ON vgh.course_id = c.id
      JOIN course_content_versions ccv ON vgh.version_id = ccv.id
      JOIN avatars_config ac ON vgh.avatar_id = ac.id
      WHERE 1=1
    `;

    const queryParams = [];
    let paramCount = 1;

    if (course_id) {
      query += ` AND vgh.course_id = $${paramCount}`;
      queryParams.push(course_id);
      paramCount++;
    }

    if (page_number) {
      query += ` AND vgh.page_number = $${paramCount}`;
      queryParams.push(page_number);
      paramCount++;
    }

    if (status) {
      query += ` AND vgh.generation_status = $${paramCount}`;
      queryParams.push(status);
      paramCount++;
    }

    query += ` ORDER BY vgh.created_at DESC`;

    const result = await pool.query(query, queryParams);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching video generation history:', err);
    res.status(500).json({ error: 'Failed to fetch video generation history', details: err.message });
  }
});

// Update video publish status (for admin use)
app.put('/api/video-generation-history/:id/publish-status', async (req, res) => {
  try {
    const { id } = req.params;
    const { video_publish_status } = req.body;

    if (!video_publish_status || !['draft', 'published', 'archived'].includes(video_publish_status)) {
      return res.status(400).json({ error: 'Invalid publish status. Must be draft, published, or archived.' });
    }

    const result = await pool.query(
      'UPDATE video_generation_history SET video_publish_status = $1 WHERE id = $2 RETURNING *',
      [video_publish_status, parseInt(id)]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json({ message: 'Video publish status updated successfully', video: result.rows[0] });
  } catch (err) {
    console.error('Error updating video publish status:', err);
    res.status(500).json({ error: 'Failed to update video publish status', details: err.message });
  }
});

// Bulk update video publish status for a course
app.put('/api/courses/:courseId/videos/publish-status', async (req, res) => {
  try {
    const { courseId } = req.params;
    const { video_publish_status } = req.body;

    if (!video_publish_status || !['draft', 'published', 'archived'].includes(video_publish_status)) {
      return res.status(400).json({ error: 'Invalid publish status. Must be draft, published, or archived.' });
    }

    const result = await pool.query(
      'UPDATE video_generation_history SET video_publish_status = $1 WHERE course_id = $2 AND generation_status = $3 RETURNING *',
      [video_publish_status, parseInt(courseId), 'success']
    );

    res.json({
      message: `Updated ${result.rows.length} videos to ${video_publish_status} status`,
      updated_videos: result.rows.length
    });
  } catch (err) {
    console.error('Error bulk updating video publish status:', err);
    res.status(500).json({ error: 'Failed to bulk update video publish status', details: err.message });
  }
});

// Delete video generation history record
app.delete('/api/video-generation-history/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM video_generation_history WHERE id = $1 RETURNING *',
      [parseInt(id)]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Video generation record not found' });
    }

    res.json({
      message: 'Video generation record deleted successfully',
      deleted_record: result.rows[0]
    });
  } catch (err) {
    console.error('Error deleting video generation record:', err);
    res.status(500).json({ error: 'Failed to delete video generation record', details: err.message });
  }
});

// Progress Tracking Endpoints

// Test endpoint to check database tables
app.get('/api/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT COUNT(*) FROM user_module_progress');
    res.json({ message: 'Database connection working', count: result.rows[0].count });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({ error: 'Database connection failed', details: error.message });
  }
});

// Start course - Initialize progress tracking (simplified - just return total modules)
app.post('/api/courses/:courseId/start', async (req, res) => {
  try {
    const { courseId } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get total modules for this course
    const videosResult = await pool.query(
      `SELECT COUNT(*) as total_modules FROM video_generation_history
       WHERE course_id = $1 AND generation_status = 'success'`,
      [parseInt(courseId)]
    );

    const totalModules = parseInt(videosResult.rows[0].total_modules) || 0;

    res.json({ message: 'Course started successfully', totalModules });
  } catch (error) {
    console.error('Error starting course:', error);
    res.status(500).json({ error: 'Failed to start course' });
  }
});

// Update module progress
app.post('/api/courses/:courseId/modules/:moduleNumber/progress', async (req, res) => {
  try {
    const { courseId, moduleNumber } = req.params;
    const { userId, watchTime, totalDuration, isCompleted, moduleTitle } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const completionPercentage = totalDuration > 0 ? Math.min((watchTime / totalDuration) * 100, 100) : 0;
    const moduleCompleted = isCompleted || completionPercentage >= 80; // Consider 80%+ as completed

    // Use a much simpler approach - separate INSERT and UPDATE
    const existingRecord = await pool.query(`
      SELECT id, first_watched_at, completed_at FROM user_module_progress
      WHERE user_id = $1 AND course_id = $2 AND module_number = $3
    `, [parseInt(userId), parseInt(courseId), parseInt(moduleNumber)]);

    if (existingRecord.rows.length > 0) {
      // Update existing record
      await pool.query(`
        UPDATE user_module_progress
        SET
          watch_time_seconds = $1,
          total_duration_seconds = $2,
          completion_percentage = $3,
          is_completed = $4,
          last_watched_at = NOW(),
          completed_at = CASE WHEN $4 = true AND completed_at IS NULL THEN NOW() ELSE completed_at END,
          updated_at = NOW()
        WHERE user_id = $5 AND course_id = $6 AND module_number = $7
      `, [
        parseInt(watchTime),
        parseInt(totalDuration),
        parseFloat(completionPercentage),
        moduleCompleted,
        parseInt(userId),
        parseInt(courseId),
        parseInt(moduleNumber)
      ]);
    } else {
      // Insert new record
      await pool.query(`
        INSERT INTO user_module_progress
        (user_id, course_id, module_number, module_title, watch_time_seconds, total_duration_seconds,
         completion_percentage, is_completed, first_watched_at, last_watched_at, completed_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW(),
                CASE WHEN $8 = true THEN NOW() ELSE NULL END, NOW())
      `, [
        parseInt(userId),
        parseInt(courseId),
        parseInt(moduleNumber),
        moduleTitle,
        parseInt(watchTime),
        parseInt(totalDuration),
        parseFloat(completionPercentage),
        moduleCompleted
      ]);
    }

    res.json({
      message: 'Module progress updated',
      completionPercentage,
      isCompleted: moduleCompleted
    });
  } catch (error) {
    console.error('Error updating module progress:', error);
    console.error('Error details:', error.message);
    console.error('Request body:', req.body);
    res.status(500).json({
      error: 'Failed to update module progress',
      details: error.message
    });
  }
});

// Get user's course progress
app.get('/api/users/:userId/courses/:courseId/progress', async (req, res) => {
  try {
    const { userId, courseId } = req.params;

    // Get module progress
    const moduleProgressResult = await pool.query(`
      SELECT * FROM user_module_progress
      WHERE user_id = $1 AND course_id = $2
      ORDER BY module_number
    `, [parseInt(userId), parseInt(courseId)]);

    // Get total modules for this course
    const totalModulesResult = await pool.query(
      `SELECT COUNT(*) as total_modules FROM video_generation_history
       WHERE course_id = $1 AND generation_status = 'success'`,
      [parseInt(courseId)]
    );

    const moduleProgress = moduleProgressResult.rows;
    const totalModules = parseInt(totalModulesResult.rows[0].total_modules) || 0;
    const completedModules = moduleProgress.filter(m => m.is_completed).length;
    const progressPercentage = totalModules > 0 ? (completedModules / totalModules) * 100 : 0;
    const status = progressPercentage >= 100 ? 'completed' : (progressPercentage > 0 ? 'in_progress' : 'not_started');

    // Calculate course progress from module data
    const courseProgress = moduleProgress.length > 0 ? {
      total_modules: totalModules,
      completed_modules: completedModules,
      progress_percentage: progressPercentage,
      status: status,
      started_at: moduleProgress[0]?.first_watched_at || null,
      completed_at: status === 'completed' ? moduleProgress.find(m => m.completed_at)?.completed_at || null : null,
      last_accessed_at: Math.max(...moduleProgress.map(m => new Date(m.last_watched_at || m.updated_at).getTime()))
    } : null;

    res.json({
      courseProgress,
      moduleProgress
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    res.status(500).json({ error: 'Failed to fetch progress' });
  }
});

// Get all courses progress for a user
app.get('/api/users/:userId/progress', async (req, res) => {
  try {
    const { userId } = req.params;

    // Get all courses the user has progress in
    const coursesResult = await pool.query(`
      SELECT DISTINCT
        ump.course_id,
        c.title,
        c.description,
        c.domain,
        c.level,
        c.estimated_hours
      FROM user_module_progress ump
      JOIN courses c ON ump.course_id = c.id
      WHERE ump.user_id = $1
      ORDER BY MAX(ump.last_watched_at) DESC
    `, [parseInt(userId)]);

    // For each course, calculate progress
    const coursesWithProgress = await Promise.all(
      coursesResult.rows.map(async (course) => {
        const moduleProgressResult = await pool.query(`
          SELECT * FROM user_module_progress
          WHERE user_id = $1 AND course_id = $2
        `, [parseInt(userId), course.course_id]);

        const totalModulesResult = await pool.query(
          `SELECT COUNT(*) as total_modules FROM video_generation_history
           WHERE course_id = $1 AND generation_status = 'success'`,
          [course.course_id]
        );

        const moduleProgress = moduleProgressResult.rows;
        const totalModules = parseInt(totalModulesResult.rows[0].total_modules) || 0;
        const completedModules = moduleProgress.filter(m => m.is_completed).length;
        const progressPercentage = totalModules > 0 ? (completedModules / totalModules) * 100 : 0;
        const status = progressPercentage >= 100 ? 'completed' : (progressPercentage > 0 ? 'in_progress' : 'not_started');

        return {
          ...course,
          total_modules: totalModules,
          completed_modules: completedModules,
          progress_percentage: progressPercentage,
          status: status,
          started_at: moduleProgress[0]?.first_watched_at || null,
          last_accessed_at: Math.max(...moduleProgress.map(m => new Date(m.last_watched_at || m.updated_at).getTime()))
        };
      })
    );

    res.json(coursesWithProgress);
  } catch (error) {
    console.error('Error fetching user progress:', error);
    res.status(500).json({ error: 'Failed to fetch user progress' });
  }
});

// Get dashboard statistics for a user
app.get('/api/users/:userId/dashboard', async (req, res) => {
  try {
    const { userId } = req.params;

    // Get all courses the user has progress in
    const coursesWithProgressResult = await pool.query(`
      SELECT DISTINCT
        ump.course_id,
        c.title,
        c.description,
        c.domain,
        c.level,
        c.estimated_hours,
        c.created_at
      FROM user_module_progress ump
      JOIN courses c ON ump.course_id = c.id
      WHERE ump.user_id = $1
    `, [parseInt(userId)]);

    // Calculate progress for each course
    const coursesWithProgress = await Promise.all(
      coursesWithProgressResult.rows.map(async (course) => {
        // Get module progress for this course
        const moduleProgressResult = await pool.query(`
          SELECT * FROM user_module_progress
          WHERE user_id = $1 AND course_id = $2
          ORDER BY module_number
        `, [parseInt(userId), course.course_id]);

        // Get total modules for this course
        const totalModulesResult = await pool.query(
          `SELECT COUNT(*) as total_modules FROM video_generation_history
           WHERE course_id = $1 AND generation_status = 'success'`,
          [course.course_id]
        );

        const moduleProgress = moduleProgressResult.rows;
        const totalModules = parseInt(totalModulesResult.rows[0].total_modules) || 0;
        const completedModules = moduleProgress.filter(m => m.is_completed).length;
        const progressPercentage = totalModules > 0 ? Math.round((completedModules / totalModules) * 100) : 0;

        let status = 'not_started';
        if (progressPercentage >= 100) {
          status = 'completed';
        } else if (progressPercentage > 0) {
          status = 'in_progress';
        }

        const startedAt = moduleProgress.length > 0 ? moduleProgress[0].first_watched_at : null;
        const lastAccessedAt = moduleProgress.length > 0 ?
          Math.max(...moduleProgress.map(m => new Date(m.last_watched_at || m.updated_at).getTime())) : null;
        const completedAt = status === 'completed' ?
          moduleProgress.find(m => m.completed_at)?.completed_at : null;

        return {
          course_id: course.course_id,
          title: course.title,
          description: course.description,
          domain: course.domain,
          level: course.level,
          estimated_hours: course.estimated_hours,
          total_modules: totalModules,
          completed_modules: completedModules,
          progress_percentage: progressPercentage,
          status: status,
          started_at: startedAt,
          last_accessed_at: lastAccessedAt,
          completed_at: completedAt,
          created_at: course.created_at
        };
      })
    );

    // Calculate dashboard statistics
    const totalCourses = coursesWithProgress.length;
    const inProgressCourses = coursesWithProgress.filter(c => c.status === 'in_progress').length;
    const completedCourses = coursesWithProgress.filter(c => c.status === 'completed').length;
    const certificatesEarned = completedCourses; // Assuming 1 certificate per completed course

    // Sort courses by last accessed (most recent first)
    coursesWithProgress.sort((a, b) => {
      const aTime = a.last_accessed_at ? new Date(a.last_accessed_at).getTime() : 0;
      const bTime = b.last_accessed_at ? new Date(b.last_accessed_at).getTime() : 0;
      return bTime - aTime;
    });

    res.json({
      statistics: {
        total_courses: totalCourses,
        in_progress_courses: inProgressCourses,
        completed_courses: completedCourses,
        certificates_earned: certificatesEarned
      },
      courses: coursesWithProgress
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
});

// Get certificates for a user (completed courses)
app.get('/api/users/:userId/certificates', async (req, res) => {
  try {
    const { userId } = req.params;

    // Get all courses the user has completed (100% progress)
    const completedCoursesResult = await pool.query(`
      SELECT DISTINCT
        ump.course_id,
        c.title,
        c.description,
        c.domain,
        c.level,
        c.estimated_hours,
        c.created_at,
        MIN(ump.first_watched_at) as started_at,
        MAX(ump.completed_at) as completed_at
      FROM user_module_progress ump
      JOIN courses c ON ump.course_id = c.id
      WHERE ump.user_id = $1 AND ump.is_completed = true
      GROUP BY ump.course_id, c.title, c.description, c.domain, c.level, c.estimated_hours, c.created_at
    `, [parseInt(userId)]);

    // Filter to only include courses where ALL modules are completed
    const certificates = await Promise.all(
      completedCoursesResult.rows.map(async (course) => {
        // Get total modules for this course
        const totalModulesResult = await pool.query(
          `SELECT COUNT(*) as total_modules FROM video_generation_history
           WHERE course_id = $1 AND generation_status = 'success'`,
          [course.course_id]
        );

        // Get completed modules for this course
        const completedModulesResult = await pool.query(`
          SELECT COUNT(*) as completed_modules FROM user_module_progress
          WHERE user_id = $1 AND course_id = $2 AND is_completed = true
        `, [parseInt(userId), course.course_id]);

        const totalModules = parseInt(totalModulesResult.rows[0].total_modules) || 0;
        const completedModules = parseInt(completedModulesResult.rows[0].completed_modules) || 0;

        // Only include if ALL modules are completed
        if (totalModules > 0 && completedModules === totalModules) {
          return {
            course_id: course.course_id,
            title: course.title,
            description: course.description,
            domain: course.domain,
            level: course.level,
            estimated_hours: course.estimated_hours,
            total_modules: totalModules,
            completed_modules: completedModules,
            started_at: course.started_at,
            completed_at: course.completed_at,
            certificate_id: `CERT-${course.course_id}-${userId}-${new Date(course.completed_at).getFullYear()}`,
            issued_date: course.completed_at
          };
        }
        return null;
      })
    );

    // Filter out null values (incomplete courses)
    const validCertificates = certificates.filter(cert => cert !== null);

    // Calculate statistics
    const totalCertificates = validCertificates.length;
    const totalLearningHours = validCertificates.reduce((sum, cert) => sum + (cert.estimated_hours || 0), 0);
    const uniqueDomains = [...new Set(validCertificates.map(cert => cert.domain))].length;

    res.json({
      certificates: validCertificates,
      statistics: {
        total_certificates: totalCertificates,
        total_learning_hours: totalLearningHours,
        unique_domains: uniqueDomains
      }
    });
  } catch (error) {
    console.error('Error fetching certificates:', error);
    res.status(500).json({ error: 'Failed to fetch certificates' });
  }
});

// Note: updateCourseProgress function removed - progress calculated dynamically from module data

// ===== DISCUSSION FORUM API ENDPOINTS =====

// Get all discussions for a specific course
app.get('/api/courses/:courseId/discussions', async (req, res) => {
  try {
    const { courseId } = req.params;

    const result = await pool.query(`
      SELECT
        cd.id,
        cd.title,
        cd.content,
        cd.created_at,
        cd.updated_at,
        u.first_name,
        u.last_name,
        u.username,
        (SELECT COUNT(*) FROM discussion_replies dr WHERE dr.discussion_id = cd.id) as reply_count
      FROM course_discussions cd
      JOIN userdetails u ON cd.user_id = u.id
      WHERE cd.course_id = $1
      ORDER BY cd.created_at DESC
    `, [parseInt(courseId)]);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching discussions:', error);
    res.status(500).json({ error: 'Failed to fetch discussions' });
  }
});

// Create a new discussion for a course
app.post('/api/courses/:courseId/discussions', async (req, res) => {
  try {
    const { courseId } = req.params;
    const { userId, title, content } = req.body;

    if (!userId || !title || !content) {
      return res.status(400).json({ error: 'User ID, title, and content are required' });
    }

    const result = await pool.query(`
      INSERT INTO course_discussions (course_id, user_id, title, content)
      VALUES ($1, $2, $3, $4)
      RETURNING id, title, content, created_at, updated_at
    `, [parseInt(courseId), parseInt(userId), title, content]);

    // Get user details for the response
    const userResult = await pool.query(`
      SELECT first_name, last_name, username FROM userdetails WHERE id = $1
    `, [parseInt(userId)]);

    const discussion = {
      ...result.rows[0],
      first_name: userResult.rows[0].first_name,
      last_name: userResult.rows[0].last_name,
      username: userResult.rows[0].username,
      reply_count: 0
    };

    res.status(201).json(discussion);
  } catch (error) {
    console.error('Error creating discussion:', error);
    res.status(500).json({ error: 'Failed to create discussion' });
  }
});

// Get replies for a specific discussion
app.get('/api/discussions/:discussionId/replies', async (req, res) => {
  try {
    const { discussionId } = req.params;

    const result = await pool.query(`
      SELECT
        dr.id,
        dr.content,
        dr.created_at,
        dr.updated_at,
        u.first_name,
        u.last_name,
        u.username
      FROM discussion_replies dr
      JOIN userdetails u ON dr.user_id = u.id
      WHERE dr.discussion_id = $1
      ORDER BY dr.created_at ASC
    `, [parseInt(discussionId)]);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching replies:', error);
    res.status(500).json({ error: 'Failed to fetch replies' });
  }
});

// Add a reply to a discussion
app.post('/api/discussions/:discussionId/replies', async (req, res) => {
  try {
    const { discussionId } = req.params;
    const { userId, content } = req.body;

    if (!userId || !content) {
      return res.status(400).json({ error: 'User ID and content are required' });
    }

    const result = await pool.query(`
      INSERT INTO discussion_replies (discussion_id, user_id, content)
      VALUES ($1, $2, $3)
      RETURNING id, content, created_at, updated_at
    `, [parseInt(discussionId), parseInt(userId), content]);

    // Get user details for the response
    const userResult = await pool.query(`
      SELECT first_name, last_name, username FROM userdetails WHERE id = $1
    `, [parseInt(userId)]);

    const reply = {
      ...result.rows[0],
      first_name: userResult.rows[0].first_name,
      last_name: userResult.rows[0].last_name,
      username: userResult.rows[0].username
    };

    res.status(201).json(reply);
  } catch (error) {
    console.error('Error creating reply:', error);
    res.status(500).json({ error: 'Failed to create reply' });
  }
});

// ===== AZURE SEARCH AND OPENAI API ENDPOINTS =====

// Utility to remove markdown and excessive whitespace from LLM output
function cleanTextForHeyGen(text) {
  // Remove markdown headers, bold, italics, lists, and extra whitespace
  return text
    .replace(/[#*_`>-]+/g, '') // Remove markdown symbols
    .replace(/\n{2,}/g, '\n') // Collapse multiple newlines
    .replace(/\s{2,}/g, ' ')   // Collapse multiple spaces
    .trim();
}

// Azure Search endpoint - Dynamic INDEX_NAME based on course_id
app.post('/api/search', async (req, res) => {
  const { query, courseId } = req.body;

  if (!courseId) {
    return res.status(400).json({ error: 'courseId is required' });
  }

  try {
    // Simple: Get index_name from pdf_indexer_tracking table based on course_id
    const result = await pool.query(
      'SELECT index_name FROM pdf_indexer_tracking WHERE course_id = $1',
      [parseInt(courseId)]
    );

    let indexName = INDEX_NAME; // fallback to static
    if (result.rows.length > 0) {
      indexName = result.rows[0].index_name;
    }

    const url = `${AZURE_SEARCH_ENDPOINT}/indexes/${indexName}/docs/search?api-version=2023-07-01-preview`;
    const body = { search: query, top: 3 };

    const response = await axios.post(url, body, {
      headers: {
        "Content-Type": "application/json",
        "api-key": AZURE_SEARCH_KEY
      }
    });
    res.json(response.data);
  } catch (err) {
    console.error('Azure Search error:', err.response?.data || err.message);
    res.status(500).json({ error: err.response?.data || err.message });
  }
});

// Rephrase endpoint
app.post('/api/rephrase', async (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text parameter is required.' });
    }

    const response = await axios.get(`http://*************:5002/rephrase?text=${encodeURIComponent(text)}`);
    res.json(response.data);
  } catch (err) {
    console.error('Rephrase API error:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to rephrase text.', details: err.response?.data || err.message });
  }
});

// Custom rephrase endpoint
app.post('/api/customrephrase', async (req, res) => {
  try {
    const { text, prompt } = req.body;

    if (!text || !prompt) {
      return res.status(400).json({ error: 'Both text and prompt parameters are required.' });
    }

    const response = await axios.get(`http://*************:5002/customrephrase?text=${encodeURIComponent(text)}&prompt=${encodeURIComponent(prompt)}`);
    res.json(response.data);
  } catch (err) {
    console.error('Custom rephrase API error:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to rephrase text with custom prompt.', details: err.response?.data || err.message });
  }
});

// HeyGen streaming token endpoint
app.post('/api/heygen/streaming-token', async (req, res) => {
  try {
    const heygenApiKey = process.env.HEYGEN_API_KEY;
    if (!heygenApiKey) {
      console.error('HeyGen API Key is missing');
      return res.status(500).json({ error: 'HeyGen API Key not configured.' });
    }

    const response = await axios.post(
      'https://api.heygen.com/v1/streaming.create_token',
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': heygenApiKey,
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('HeyGen streaming token error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    res.status(500).json({
      error: 'Failed to create HeyGen streaming token',
      details: error.response?.data || error.message
    });
  }
});

// HeyGen create new session endpoint
app.post('/api/heygen/create-session', async (req, res) => {
  try {
    const { avatarID, voiceID } = req.body;
    const heygenApiKey = process.env.HEYGEN_API_KEY;

    if (!heygenApiKey) {
      console.error('HeyGen API Key is missing');
      return res.status(500).json({ error: 'HeyGen API Key not configured.' });
    }

    // First get a session token
    const tokenResponse = await axios.post(
      'https://api.heygen.com/v1/streaming.create_token',
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': heygenApiKey,
        },
      }
    );

    const token = tokenResponse.data.data.token;

    // Create new session
    const sessionResponse = await axios.post(
      'https://api.heygen.com/v1/streaming.new',
      {
        quality: "high",
        avatar_name: avatarID || 'Pedro_Chair_Sitting_public',
        voice: {
          voice_id: voiceID || '',
          rate: 1.0,
        },
        version: "v2",
        video_encoding: "H264",
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    res.json({
      sessionData: sessionResponse.data.data,
      token: token
    });
  } catch (error) {
    console.error('HeyGen create session error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    res.status(500).json({
      error: 'Failed to create HeyGen session',
      details: error.response?.data || error.message
    });
  }
});

// HeyGen start streaming session endpoint
app.post('/api/heygen/start-session', async (req, res) => {
  try {
    const { sessionId, token } = req.body;

    if (!sessionId || !token) {
      return res.status(400).json({ error: 'Session ID and token are required.' });
    }

    const response = await axios.post(
      'https://api.heygen.com/v1/streaming.start',
      {
        session_id: sessionId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('HeyGen start session error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    res.status(500).json({
      error: 'Failed to start HeyGen session',
      details: error.response?.data || error.message
    });
  }
});

// HeyGen send text to avatar endpoint
app.post('/api/heygen/send-text', async (req, res) => {
  try {
    const { sessionId, token, text, taskType = 'talk' } = req.body;

    if (!sessionId || !token || !text) {
      return res.status(400).json({ error: 'Session ID, token, and text are required.' });
    }

    const response = await axios.post(
      'https://api.heygen.com/v1/streaming.task',
      {
        session_id: sessionId,
        text: text,
        task_type: taskType,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('HeyGen send text error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    res.status(500).json({
      error: 'Failed to send text to HeyGen avatar',
      details: error.response?.data || error.message
    });
  }
});

// HeyGen close session endpoint
app.post('/api/heygen/close-session', async (req, res) => {
  try {
    const { sessionId, token } = req.body;

    if (!sessionId || !token) {
      return res.status(400).json({ error: 'Session ID and token are required.' });
    }

    const response = await axios.post(
      'https://api.heygen.com/v1/streaming.stop',
      {
        session_id: sessionId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('HeyGen close session error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    res.status(500).json({
      error: 'Failed to close HeyGen session',
      details: error.response?.data || error.message
    });
  }
});

// OpenAI endpoint
app.post('/api/openai', async (req, res) => {
  const { prompt } = req.body;
  const url = `${OPENAI_ENDPOINT}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-08-01-preview`;

  // Debug logging
  console.log('OpenAI Request Details:');
  console.log('- Endpoint:', OPENAI_ENDPOINT);
  console.log('- Deployment:', OPENAI_DEPLOYMENT);
  console.log('- Full URL:', url);
  console.log('- API Key (first 10 chars):', OPENAI_KEY ? OPENAI_KEY.substring(0, 10) + '...' : 'NOT SET');

  // Add instruction to restrict answer to 30 words
  const body = {
    messages: [
      // { role: "system", content: "You are a helpful assistant. When answering, respond in plain English and do not use markdown or formatting. Your answer must be a single sentence and no more than 30 words." },
       { role: "system", content: "You are a helpful assistant. When answering, respond in plain English and do not use markdown or formatting." },
      { role: "user", content: prompt }
    ],
    temperature: 0.0,
    max_tokens: 512
  };
  try {
    const response = await axios.post(url, body, {
      headers: {
        "Content-Type": "application/json",
        "api-key": OPENAI_KEY
      }
    });
    const data = response.data;
    // Clean the LLM answer before sending to frontend
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      data.choices[0].message.content = cleanTextForHeyGen(data.choices[0].message.content);
    }
    res.json(data);
  } catch (err) {
    console.error('OpenAI error details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message
    });
    res.status(500).json({ error: err.response?.data || err.message });
  }
});




// Approve assessment endpoint
app.post('/api/assessments/approve', verifyToken, async (req, res) => {
  try {
    const { course_id, course_title, course_domain, course_content, questions, approved_by } = req.body;

    if (!course_id || !course_title || !questions || !Array.isArray(questions)) {
      return res.status(400).json({ error: 'Missing required fields: course_id, course_title, questions' });
    }

    // Start transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Insert approved assessment
      const assessmentResult = await client.query(
        `INSERT INTO approved_assessments
         (course_id, course_title, course_domain, course_content, total_questions, approved_by)
         VALUES ($1, $2, $3, $4, $5, $6)
         RETURNING id`,
        [course_id, course_title, course_domain, course_content, questions.length, approved_by]
      );

      const assessmentId = assessmentResult.rows[0].id;

      // Insert questions
      for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        const { question: questionText, possible_answers, correct_answer } = question;

        if (!questionText || !possible_answers || possible_answers.length !== 4 || !correct_answer) {
          throw new Error(`Invalid question format at index ${i}`);
        }

        // Determine correct answer letter (A, B, C, D)
        const correctAnswerIndex = possible_answers.findIndex(answer => answer === correct_answer);
        if (correctAnswerIndex === -1) {
          throw new Error(`Correct answer not found in possible answers for question ${i + 1}`);
        }
        const correctAnswerLetter = String.fromCharCode(65 + correctAnswerIndex); // A=65, B=66, C=67, D=68

        await client.query(
          `INSERT INTO assessment_questions
           (assessment_id, question_number, question_text, option_a, option_b, option_c, option_d, correct_answer)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            assessmentId,
            i + 1,
            questionText,
            possible_answers[0],
            possible_answers[1],
            possible_answers[2],
            possible_answers[3],
            correctAnswerLetter
          ]
        );
      }

      await client.query('COMMIT');

      res.status(201).json({
        message: 'Assessment approved and stored successfully',
        assessment_id: assessmentId,
        total_questions: questions.length
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (err) {
    console.error('Error approving assessment:', err);
    res.status(500).json({
      error: 'Failed to approve assessment',
      details: err.message
    });
  }
});

// Get approved assessments endpoint
app.get('/api/assessments/approved', verifyToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        aa.id,
        aa.course_id,
        aa.course_title,
        aa.course_domain,
        aa.total_questions,
        aa.approved_by,
        aa.approved_at,
        aa.status
      FROM approved_assessments aa
      WHERE aa.status = 'active'
      ORDER BY aa.approved_at DESC
    `);

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching approved assessments:', err);
    res.status(500).json({ error: 'Failed to fetch approved assessments' });
  }
});

// Get specific approved assessment with questions
app.get('/api/assessments/approved/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Get assessment details
    const assessmentResult = await pool.query(`
      SELECT * FROM approved_assessments WHERE id = $1 AND status = 'active'
    `, [id]);

    if (assessmentResult.rows.length === 0) {
      return res.status(404).json({ error: 'Approved assessment not found' });
    }

    // Get questions
    const questionsResult = await pool.query(`
      SELECT * FROM assessment_questions
      WHERE assessment_id = $1
      ORDER BY question_number
    `, [id]);

    const assessment = assessmentResult.rows[0];
    assessment.questions = questionsResult.rows;

    res.json(assessment);
  } catch (err) {
    console.error('Error fetching approved assessment:', err);
    res.status(500).json({ error: 'Failed to fetch approved assessment' });
  }
});







// Set port
const PORT = process.env.PORT || 5001;

// Start server with Socket.IO
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Socket.IO server is ready for real-time notifications`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  notificationService.stopPolling();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});